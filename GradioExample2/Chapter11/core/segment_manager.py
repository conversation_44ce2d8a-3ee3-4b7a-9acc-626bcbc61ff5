import json
import os

class SegmentManager:
    def __init__(self):
        pass
    
    def save_segments(self, segments, file_path):
        """保存片段信息到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(segments, f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise Exception(f"保存片段信息失败: {str(e)}")
    
    def load_segments(self, file_path):
        """从文件加载片段信息"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            raise Exception(f"加载片段信息失败: {str(e)}")
    
    def export_segments_info(self, segments, video_path, output_path):
        """导出片段信息为文本文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(f"视频文件: {os.path.basename(video_path)}\n")
                f.write(f"总片段数: {len(segments)}\n")
                f.write("=" * 50 + "\n\n")
                
                for i, segment in enumerate(segments):
                    start_time = self.format_time(segment['start'])
                    end_time = self.format_time(segment['end'])
                    duration = segment['end'] - segment['start']
                    
                    f.write(f"片段 {i+1}:\n")
                    f.write(f"时间: {start_time} - {end_time} (时长: {duration:.1f}秒)\n")
                    f.write(f"内容: {segment['text']}\n")
                    f.write("-" * 30 + "\n\n")
        except Exception as e:
            raise Exception(f"导出片段信息失败: {str(e)}")
    
    def format_time(self, seconds):
        """格式化时间显示"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"