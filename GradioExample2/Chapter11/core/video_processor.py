import os
import subprocess
from PyQt5.QtCore import pyqtSignal

class VideoProcessor:
    def __init__(self):
        self.ffmpeg_path = self.find_ffmpeg()
    
    def find_ffmpeg(self):
        """查找FFmpeg可执行文件"""
        try:
            result = subprocess.run(['which', 'ffmpeg'], capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass
        
        # 常见路径
        common_paths = [
            '/usr/local/bin/ffmpeg',
            '/usr/bin/ffmpeg',
            '/opt/homebrew/bin/ffmpeg'
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return path
        
        raise Exception("未找到FFmpeg，请确保已安装FFmpeg")
    
    def split_video(self, video_path, segments, output_dir, progress_callback=None):
        """根据片段分割视频"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        total_segments = len(segments)
        base_name = os.path.splitext(os.path.basename(video_path))[0]
        
        for i, segment in enumerate(segments):
            try:
                # 生成输出文件名
                start_time_str = self.format_time_for_filename(segment['start'])
                output_filename = f"{base_name}_片段{i+1:03d}_{start_time_str}.mp4"
                output_path = os.path.join(output_dir, output_filename)
                
                # 打印调试信息
                duration = segment['end'] - segment['start']
                print(f"片段 {i+1}: 开始={segment['start']:.2f}s, 结束={segment['end']:.2f}s, 时长={duration:.2f}s")
                
                # 构建FFmpeg命令
                cmd = [
                    self.ffmpeg_path,
                    '-ss', str(segment['start']),
                    '-i', video_path,
                    '-t', str(duration),
                    '-c', 'copy',
                    '-avoid_negative_ts', 'make_zero',
                    '-y',
                    output_path
                ]
                
                # 打印FFmpeg命令用于调试
                print(f"FFmpeg命令: {' '.join(cmd)}")
                
                # 执行FFmpeg命令
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode != 0:
                    raise Exception(f"FFmpeg错误: {result.stderr}")
                
                # 更新进度
                if progress_callback:
                    progress = int((i + 1) / total_segments * 100)
                    progress_callback.emit(progress)
                
            except Exception as e:
                raise Exception(f"分割片段 {i+1} 失败: {str(e)}")
    
    def format_time_for_filename(self, seconds):
        """格式化时间用于文件名"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        return f"{hours:02d}h{minutes:02d}m{seconds:02d}s"
    
    def export_single_segment(self, video_path, segment, output_dir, filename, progress_callback=None):
        """导出单个视频片段"""
        try:
            output_path = os.path.join(output_dir, filename)
            
            # 构建 FFmpeg 命令
            start_time = segment['start']
            duration = segment['end'] - segment['start']
            
            cmd = [
                'ffmpeg',
                '-y',  # 覆盖输出文件
                '-ss', str(start_time),  # 开始时间
                '-i', video_path,  # 输入文件
                '-t', str(duration),  # 持续时间
                '-c', 'copy',  # 流复制，快速处理
                output_path  # 输出文件
            ]
            
            print(f"执行命令: {' '.join(cmd)}")
            
            # 执行命令
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 等待完成
            stdout, stderr = process.communicate()
            
            if process.returncode != 0:
                raise Exception(f"FFmpeg 错误: {stderr}")
            
            if progress_callback:
                progress_callback.emit(100)
                
            print(f"片段导出完成: {output_path}")
            
        except Exception as e:
            print(f"导出片段失败: {e}")
            raise