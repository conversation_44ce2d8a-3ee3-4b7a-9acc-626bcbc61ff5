import whisper
import torch


class WhisperTranscriber:
    def __init__(self, model_size="base"):
        self.model_size = model_size
        self.model = None
        self.load_model()
    
    def load_model(self):
        """加载Whisper模型"""
        try:
            device = "cuda" if torch.cuda.is_available() else "cpu"
            self.model = whisper.load_model(self.model_size, device=device)
            print(f"Whisper模型已加载: {self.model_size}, 设备: {device}")
        except Exception as e:
            raise Exception(f"加载Whisper模型失败: {str(e)}")
    
    def transcribe(self, video_path, progress_callback=None):
        """转录视频并返回带时间戳的片段"""
        if not self.model:
            raise Exception("Whisper模型未加载")
        
        try:
            # 使用Whisper转录
            if progress_callback:
                progress_callback.emit(10)
            
            result = self.model.transcribe(
                video_path,
                word_timestamps=True,
                verbose=True
            )
            
            if progress_callback:
                progress_callback.emit(50)
            
            # 处理转录结果，创建智能分割点
            segments = self.create_smart_segments(result)
            
            if progress_callback:
                progress_callback.emit(100)
            
            return segments
            
        except Exception as e:
            raise Exception(f"转录失败: {str(e)}")
    
    def create_smart_segments(self, whisper_result):
        """基于转录结果（包含词级时间戳）创建更符合语义的分割片段"""
        segments_info = []
        current_segment_words = []
        current_segment_start_time = -1

        # 遍历 Whisper 返回的原始片段
        for seg_idx, segment_data in enumerate(whisper_result['segments']):
            # 遍历片段中的每个词
            for word_idx, word_info in enumerate(segment_data.get('words', [])):
                word_text = word_info['word'].strip()
                word_start = word_info['start']
                word_end = word_info['end']

                if not current_segment_words:
                    current_segment_start_time = word_start
                
                current_segment_words.append(word_text)

                # 语义断点判断逻辑 (初步)
                # 1. 句末标点 (中文或英文)
                # 2. 词间有较长停顿 (例如 > 0.7秒)
                # 3. 片段本身结束
                is_sentence_end_punctuation = any(punc in word_text for punc in ['。', '！', '？', '.', '!', '?'])
                
                next_word_start_time = None
                if word_idx + 1 < len(segment_data.get('words', [])):
                    next_word_start_time = segment_data['words'][word_idx+1]['start']
                elif seg_idx + 1 < len(whisper_result['segments']):
                    # 检查下一个segment的第一个词
                    next_segment_words = whisper_result['segments'][seg_idx+1].get('words', [])
                    if next_segment_words:
                        next_word_start_time = next_segment_words[0]['start']
                
                has_long_pause = False
                if next_word_start_time is not None:
                    pause_duration = next_word_start_time - word_end
                    if pause_duration > 0.7: # 可调整的停顿阈值
                        has_long_pause = True
                
                # 如果是最后一个词，则强制结束当前片段
                is_last_word_in_transcription = (seg_idx == len(whisper_result['segments']) - 1 and 
                                                 word_idx == len(segment_data.get('words', [])) - 1)

                if current_segment_words and (is_sentence_end_punctuation or has_long_pause or is_last_word_in_transcription):
                    segment_text = " ".join(current_segment_words)
                    # 确保即使只有一个词且是标点，也能正确处理
                    if segment_text.strip(): # 避免空片段
                        segments_info.append({
                            'start': current_segment_start_time,
                            'end': word_end,
                            'text': segment_text.strip(),
                            'confidence': segment_data.get('avg_logprob', 0) # 可以考虑词的平均置信度
                        })
                    current_segment_words = []
                    current_segment_start_time = -1

        # 如果循环结束后仍有未处理的词 (不太可能，因为有 is_last_word_in_transcription)
        if current_segment_words:
            final_text = " ".join(current_segment_words)
            if final_text.strip(): # 避免空片段
                # 尝试获取最后一个词的结束时间
                last_word_end_time = whisper_result['segments'][-1]['words'][-1]['end'] if whisper_result['segments'] and whisper_result['segments'][-1].get('words') else whisper_result['segments'][-1]['end']
                segments_info.append({
                    'start': current_segment_start_time,
                    'end': last_word_end_time,
                    'text': final_text.strip(),
                    'confidence': whisper_result['segments'][-1].get('avg_logprob', 0) if whisper_result['segments'] else 0
                })

        # 合并短片段，分割长片段 (这里的逻辑也需要围绕语义调整)
        optimized_segments = self.optimize_segments_by_semantics(segments_info)
        
        return optimized_segments

    def is_natural_break_point(self, text, previous_word_end=None, current_word_start=None):
        """检测是否为自然语义分割点 (此函数可能被新的逻辑取代或重构)
           text: 当前词或短语
           previous_word_end: 前一个词的结束时间
           current_word_start: 当前词的开始时间
        """
        break_indicators = ['。', '！', '？', '.', '!', '?']
        if any(indicator in text for indicator in break_indicators):
            return True
        
        if previous_word_end is not None and current_word_start is not None:
            pause_duration = current_word_start - previous_word_end
            if pause_duration > 0.7: # 停顿阈值，可调整
                return True
        return False

    def optimize_segments_by_semantics(self, segments, min_duration=2.0, max_duration=15.0, prefer_natural_breaks=True):
        """优化片段长度，尽量保持语义完整性"""
        if not segments:
            return []

        optimized = []
        current_segment_text = []
        current_segment_start = -1
        current_segment_end = -1
        # last_segment_was_natural_break = True # 用于判断是否可以安全合并

        for i, seg in enumerate(segments):
            seg_text = seg['text']
            seg_start = seg['start']
            seg_end = seg['end']
            seg_duration = seg_end - seg_start

            if current_segment_start == -1: # 开始新片段
                current_segment_text = [seg_text]
                current_segment_start = seg_start
                current_segment_end = seg_end
            else:
                potential_combined_duration = seg_end - current_segment_start
                # 尝试合并的条件：
                # 1. 当前片段 + seg 太短 (< min_duration)
                # 2. 当前片段本身未达到min_duration，且seg不是一个强语义断点 (例如，不是句末)
                # 3. 合并后不超过 max_duration
                
                # 简化逻辑：如果当前片段太短，就尝试合并，除非合并后太长
                if (current_segment_end - current_segment_start < min_duration or seg_duration < min_duration) and potential_combined_duration <= max_duration:
                    current_segment_text.append(seg_text)
                    current_segment_end = seg_end
                # 如果当前片段过长，或者当前片段已合适且新seg也合适，则分割
                elif (current_segment_end - current_segment_start) > max_duration: 
                    # 先保存超长的部分 (尝试在内部找断点，但这里简化为直接切分)
                    optimized.append({
                        'start': current_segment_start,
                        'end': current_segment_end, # 或者找到一个合适的切分点
                        'text': " ".join(current_segment_text).strip()
                    })
                    # 开始新的
                    current_segment_text = [seg_text]
                    current_segment_start = seg_start
                    current_segment_end = seg_end
                else: # 当前片段长度合适，新片段也独立成段
                    optimized.append({
                        'start': current_segment_start,
                        'end': current_segment_end,
                        'text': " ".join(current_segment_text).strip()
                    })
                    current_segment_text = [seg_text]
                    current_segment_start = seg_start
                    current_segment_end = seg_end
            
            # 如果是最后一个片段，确保它被添加
            if i == len(segments) - 1 and current_segment_start != -1:
                optimized.append({
                    'start': current_segment_start,
                    'end': current_segment_end,
                    'text': " ".join(current_segment_text).strip()
                })

        # 后处理：再次检查是否有过长或过短的片段，并尝试调整
        # 这一步可以更复杂，例如，如果一个片段太长，尝试在内部根据词级时间戳找最佳分割点
        final_segments = []
        for opt_seg in optimized:
            duration = opt_seg['end'] - opt_seg['start']
            if min_duration <= duration <= max_duration:
                final_segments.append(opt_seg)
            elif duration < min_duration and final_segments: # 太短，尝试合并到前一个
                # 简单合并，实际可能需要更智能的判断
                if (final_segments[-1]['end'] - final_segments[-1]['start']) + duration <= max_duration:
                    final_segments[-1]['text'] += " " + opt_seg['text']
                    final_segments[-1]['end'] = opt_seg['end']
                else:
                    final_segments.append(opt_seg) # 无法合并，保留
            elif duration > max_duration: # 太长，这里简单保留，理想情况是再次分割
                # TODO: 实现更智能的长片段分割逻辑，例如基于内部词的停顿或标点
                # 暂时先按原样添加，或按max_duration强行切分 (会导致语义不完整)
                # For now, just add it, or split it naively
                # final_segments.append(opt_seg)
                # Naive split by max_duration, might break semantics
                num_sub_segments = int(duration // max_duration) + 1
                sub_segment_duration = duration / num_sub_segments
                temp_start = opt_seg['start']
                # This split is purely by time, not ideal for semantics
                # A better approach would be to find word boundaries within the long segment
                # For simplicity in this iteration, we'll just add the long segment as is.
                final_segments.append(opt_seg) 
            else: # 其他情况 (例如第一个片段太短)
                 final_segments.append(opt_seg)
        
        # 移除文本为空的片段
        final_segments = [s for s in final_segments if s['text']]
        return final_segments

    def optimize_segments(self, segments, min_duration=1, max_duration=10):
        """旧的优化片段长度方法，现在被 optimize_segments_by_semantics 替代或辅助。"""
        # 可以保留这个方法作为一个备选，或者将其逻辑融入新的方法中
        # 当前，我们将主要依赖 optimize_segments_by_semantics
        print("Warning: optimize_segments is being called, but optimize_segments_by_semantics is preferred.")
        # For now, let's just pass through to the new method with default semantic parameters
        # return self.optimize_segments_by_semantics(segments, min_duration=min_duration, max_duration=max_duration)
        
        # 保持旧的实现，以防直接调用，但建议更新调用点
        optimized = []
        current_segment = None
        
        for segment in segments:
            duration = segment['end'] - segment['start']
            
            if current_segment is None:
                current_segment = segment.copy()
            elif duration < min_duration and not segment.get('is_break_point', False): # is_break_point可能不再准确
                current_segment['end'] = segment['end']
                current_segment['text'] += ' ' + segment['text']
            elif (current_segment['end'] - current_segment['start']) > max_duration:
                optimized.append(current_segment)
                current_segment = segment.copy()
            else:
                optimized.append(current_segment)
                current_segment = segment.copy()
        
        if current_segment:
            optimized.append(current_segment)
        
        return optimized