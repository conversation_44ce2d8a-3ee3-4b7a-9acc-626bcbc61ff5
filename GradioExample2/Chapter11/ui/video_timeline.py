from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPainter, QColor, QFont, QPen

class TimelineSegment(QWidget):
    clicked = pyqtSignal(int)
    
    def __init__(self, segment_index, start_time, end_time, text, duration_ratio):
        super().__init__()
        self.segment_index = segment_index
        self.start_time = start_time
        self.end_time = end_time
        self.text = text
        self.duration_ratio = duration_ratio
        self.setMinimumHeight(60)
        self.setToolTip(f"{self.format_time(start_time)} - {self.format_time(end_time)}\n{text[:100]}...")
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制片段背景
        rect = self.rect()
        colors = [QColor(100, 150, 200), QColor(150, 200, 100), QColor(200, 150, 100), 
                 QColor(200, 100, 150), QColor(150, 100, 200), QColor(100, 200, 150)]
        color = colors[self.segment_index % len(colors)]
        painter.fillRect(rect, color)
        
        # 绘制边框
        painter.setPen(QPen(QColor(50, 50, 50), 2))
        painter.drawRect(rect)
        
        # 绘制文本
        painter.setPen(QColor(255, 255, 255))
        font = QFont()
        font.setPointSize(10)
        font.setBold(True)
        painter.setFont(font)
        
        # 时间标签
        time_text = f"{self.format_time(self.start_time)} - {self.format_time(self.end_time)}"
        painter.drawText(rect.adjusted(5, 5, -5, -35), Qt.AlignLeft | Qt.AlignTop, time_text)
        
        # 文本预览
        preview_text = self.text[:30] + '...' if len(self.text) > 30 else self.text
        font.setPointSize(8)
        font.setBold(False)
        painter.setFont(font)
        painter.drawText(rect.adjusted(5, 25, -5, -5), Qt.AlignLeft | Qt.AlignTop, preview_text)
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.segment_index)
    
    def format_time(self, seconds):
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

class VideoTimeline(QWidget):
    segment_clicked = pyqtSignal(int)
    
    def __init__(self):
        super().__init__()
        self.segments = []
        self.total_duration = 0
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        
        # 时间轴标题
        title_label = QLabel('视频时间轴 - 根据转录自动分割')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        self.timeline_widget = QWidget()
        self.timeline_layout = QVBoxLayout(self.timeline_widget)
        self.timeline_layout.setSpacing(2)
        
        self.scroll_area.setWidget(self.timeline_widget)
        layout.addWidget(self.scroll_area)
    
    def set_segments(self, segments):
        self.segments = segments
        if segments:
            self.total_duration = segments[-1]['end']
        
        # 更安全的清除现有片段方法
        while self.timeline_layout.count():
            child = self.timeline_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # 添加新片段
        for i, segment in enumerate(segments):
            duration = segment['end'] - segment['start']
            duration_ratio = duration / self.total_duration if self.total_duration > 0 else 0
            
            segment_widget = TimelineSegment(
                i, segment['start'], segment['end'], 
                segment['text'], duration_ratio
            )
            segment_widget.clicked.connect(self.segment_clicked.emit)
            
            # 根据持续时间设置宽度
            min_width = max(200, int(duration_ratio * 800))
            segment_widget.setMinimumWidth(min_width)
            
            self.timeline_layout.addWidget(segment_widget)
        
        self.timeline_layout.addStretch()