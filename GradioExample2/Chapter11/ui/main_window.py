import os
import sys
import vlc
from PyQt5.QtWidgets import (
    QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, 
    QLabel, QProgressBar, QFileDialog, QMessageBox, QSplitter,
    QListWidget, QTextEdit, QGroupBox, QSlider, QFrame
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer, QStandardPaths
from PyQt5.QtGui import QFont
from .video_timeline import VideoTimeline
from core.whisper_transcriber import WhisperTranscriber
from core.video_processor import VideoProcessor
from core.segment_manager import SegmentManager

class TranscriptionWorker(QThread):
    progress = pyqtSignal(int)
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    
    def __init__(self, video_path):
        super().__init__()
        self.video_path = video_path
        self.transcriber = WhisperTranscriber()
    
    def run(self):
        try:
            segments = self.transcriber.transcribe(self.video_path, self.progress)
            self.finished.emit(segments)
        except Exception as e:
            self.error.emit(str(e))

class VideoProcessingWorker(QThread):
    progress = pyqtSignal(int)
    finished = pyqtSignal()
    error = pyqtSignal(str)
    
    def __init__(self, video_path, segments, output_dir):
        super().__init__()
        self.video_path = video_path
        self.segments = segments
        self.output_dir = output_dir
        self.processor = VideoProcessor()
    
    def run(self):
        try:
            self.processor.split_video(self.video_path, self.segments, self.output_dir, self.progress)
            self.finished.emit()
        except Exception as e:
            self.error.emit(str(e))

class VideoExportWorker(QThread):
    progress = pyqtSignal(int)
    finished = pyqtSignal()
    error = pyqtSignal(str)
    
    def __init__(self, video_path, segments, output_dir, custom_filename=None):
        super().__init__()
        self.video_path = video_path
        self.segments = segments
        self.output_dir = output_dir
        self.custom_filename = custom_filename
        self.processor = VideoProcessor()
    
    def run(self):
        try:
            if self.custom_filename:
                # 单片段导出，使用自定义文件名
                self.processor.export_single_segment(
                    self.video_path, 
                    self.segments[0], 
                    self.output_dir,
                    self.custom_filename,
                    self.progress
                )
            else:
                # 批量导出
                self.processor.split_video(
                    self.video_path, 
                    self.segments, 
                    self.output_dir, 
                    self.progress
                )
            self.finished.emit()
        except Exception as e:
            self.error.emit(str(e))

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.video_path = None
        self.vlc_instance = None
        self.vlc_player = None
        self.is_playing = False
        self.segments = []
        self.segment_manager = SegmentManager()
        self.current_segment_index = -1
        self.current_segment_end_time = -1
        
        # 初始化 VLC
        self.vlc_instance = None
        self.vlc_player = None
        self.is_playing = False
        
        self.init_ui()
        self.init_vlc_player()
        
        # 创建定时器用于更新播放进度
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_position)
        self.timer.start(100)  # 每100ms更新一次
    
    def init_vlc_player(self):
        """初始化 VLC 播放器"""
        try:
            # 创建 VLC 实例
            self.vlc_instance = vlc.Instance()
            self.vlc_player = self.vlc_instance.media_player_new()
            
            # 设置视频输出到 QFrame
            if sys.platform == "darwin":  # macOS
                self.vlc_player.set_nsobject(int(self.video_widget.winId()))
            elif sys.platform == "win32":  # Windows
                self.vlc_player.set_hwnd(int(self.video_widget.winId()))
            else:  # Linux
                self.vlc_player.set_xwindow(int(self.video_widget.winId()))
            
            # 设置音量
            self.vlc_player.audio_set_volume(50)
            
            print("VLC 播放器初始化成功")
            
        except Exception as e:
            print(f"VLC 播放器初始化失败: {e}")
            QMessageBox.warning(self, '警告', 
                f'VLC 播放器初始化失败，视频预览功能可能不可用。\n错误: {str(e)}')
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('视频转录分割工具 (VLC 后端)')
        self.setGeometry(100, 100, 1400, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([800, 600])
        
        # 创建状态栏
        self.statusBar().showMessage('准备就绪 - 使用 VLC 后端')
    
    def create_left_panel(self):
        """创建左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 控制按钮组
        controls_group = QGroupBox('控制面板')
        controls_layout = QVBoxLayout(controls_group)
        
        # 加载视频按钮
        self.load_video_btn = QPushButton('加载视频')
        self.load_video_btn.clicked.connect(self.load_video)
        controls_layout.addWidget(self.load_video_btn)
        
        # 视频信息标签
        self.video_label = QLabel('未选择视频文件')
        controls_layout.addWidget(self.video_label)
        
        # 开始转录按钮
        self.transcribe_btn = QPushButton('开始转录')
        self.transcribe_btn.setEnabled(False)
        self.transcribe_btn.clicked.connect(self.start_transcription)
        controls_layout.addWidget(self.transcribe_btn)
        
        # 分割视频按钮
        self.split_video_btn = QPushButton('分割视频')
        self.split_video_btn.setEnabled(False)
        self.split_video_btn.clicked.connect(self.split_video)
        controls_layout.addWidget(self.split_video_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        controls_layout.addWidget(self.progress_bar)
        
        left_layout.addWidget(controls_group)
        
        # 视频时间轴
        timeline_group = QGroupBox('视频时间轴')
        timeline_layout = QVBoxLayout(timeline_group)
        
        self.video_timeline = VideoTimeline()
        self.video_timeline.segment_clicked.connect(self.on_segment_clicked)
        timeline_layout.addWidget(self.video_timeline)
        
        left_layout.addWidget(timeline_group)
        
        return left_widget
    
    def create_right_panel(self):
        """创建右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 视频预览区域
        preview_group = QGroupBox('视频预览 (VLC)')
        preview_layout = QVBoxLayout(preview_group)
        
        # 视频显示组件 - 使用 QFrame 替代 QVideoWidget
        self.video_widget = QFrame()
        self.video_widget.setMinimumHeight(200)
        self.video_widget.setStyleSheet("background-color: black;")
        preview_layout.addWidget(self.video_widget)
        
        # 播放控制
        controls_layout = QHBoxLayout()
        
        self.play_pause_btn = QPushButton('播放')
        self.play_pause_btn.setEnabled(False)
        self.play_pause_btn.clicked.connect(self.toggle_playback)
        controls_layout.addWidget(self.play_pause_btn)
        
        self.stop_btn = QPushButton('停止')
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_playback)
        controls_layout.addWidget(self.stop_btn)
        
        # 进度条
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.sliderPressed.connect(self.slider_pressed)
        self.position_slider.sliderReleased.connect(self.slider_released)
        controls_layout.addWidget(self.position_slider)
        
        # 时间标签
        self.time_label = QLabel('00:00:00 / 00:00:00')
        controls_layout.addWidget(self.time_label)
        
        preview_layout.addLayout(controls_layout)
        
        # 音量控制
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(QLabel('音量:'))
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(50)
        self.volume_slider.valueChanged.connect(self.set_volume)
        volume_layout.addWidget(self.volume_slider)
        
        preview_layout.addLayout(volume_layout)
        
        # 当前片段信息
        self.current_segment_label = QLabel('当前片段: 无')
        preview_layout.addWidget(self.current_segment_label)
        
        # 导出当前片段按钮
        self.export_segment_btn = QPushButton('导出当前视频片段')
        self.export_segment_btn.setEnabled(False)
        self.export_segment_btn.clicked.connect(self.export_current_segment)
        self.export_segment_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        preview_layout.addWidget(self.export_segment_btn)
        
        right_layout.addWidget(preview_group)
        
        # 转录文本显示
        transcript_group = QGroupBox('转录文本')
        transcript_layout = QVBoxLayout(transcript_group)
        
        self.transcript_text = QTextEdit()
        self.transcript_text.setReadOnly(True)
        self.transcript_text.setFont(QFont('Arial', 10))
        transcript_layout.addWidget(self.transcript_text)
        
        right_layout.addWidget(transcript_group)
        
        # 片段列表
        segments_group = QGroupBox('片段列表')
        segments_layout = QVBoxLayout(segments_group)
        
        self.segments_list = QListWidget()
        self.segments_list.itemClicked.connect(self.on_segment_list_clicked)
        segments_layout.addWidget(self.segments_list)
        
        right_layout.addWidget(segments_group)
        
        return right_widget
    
    def load_video(self):
        """加载视频文件"""
        # 设置默认目录
        default_dir = QStandardPaths.writableLocation(QStandardPaths.MoviesLocation)
        if not default_dir or not os.path.exists(default_dir):
            default_dir = os.path.expanduser('~')
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, '选择视频文件', default_dir,
            'Video Files (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v);;All Files (*)'
        )
        
        if file_path:
            self.video_path = file_path
            self.video_label.setText(f'已选择: {os.path.basename(file_path)}')
            self.transcribe_btn.setEnabled(True)
            
            # 加载视频到 VLC 播放器
            try:
                if self.vlc_player:
                    media = self.vlc_instance.media_new(file_path)
                    self.vlc_player.set_media(media)
                    self.play_pause_btn.setEnabled(True)
                    self.stop_btn.setEnabled(True)
                    
                    # 获取视频时长
                    media.parse()
                    duration = media.get_duration()
                    if duration > 0:
                        self.position_slider.setRange(0, duration)
                    
            except Exception as e:
                print(f"视频加载失败: {e}")
                QMessageBox.information(self, '提示', 
                    '视频文件已选择，但预览功能可能不可用。转录功能仍然正常。')
            
            self.statusBar().showMessage(f'已加载视频: {os.path.basename(file_path)}')
    
    def toggle_playback(self):
        """切换播放/暂停状态"""
        if not self.vlc_player:
            return
        
        if self.is_playing:
            self.vlc_player.pause()
            self.play_pause_btn.setText('播放')
            self.is_playing = False
        else:
            # 如果有选中的片段，确保在片段范围内播放
            if (self.current_segment_index >= 0 and 
                hasattr(self, 'current_segment_end_time') and
                self.current_segment_end_time > 0):
                current_time = self.vlc_player.get_time()
                if current_time >= self.current_segment_end_time:
                    # 如果已经超过片段结束时间，重新从片段开始播放
                    segment = self.segments[self.current_segment_index]
                    start_ms = int(segment['start'] * 1000)
                    self.vlc_player.set_time(start_ms)
            
            self.vlc_player.play()
            self.play_pause_btn.setText('暂停')
            self.is_playing = True
    
    def stop_playback(self):
        """停止播放"""
        if self.vlc_player:
            self.vlc_player.stop()
            self.play_pause_btn.setText('播放')
            self.is_playing = False
    
    def set_volume(self, volume):
        """设置音量"""
        if self.vlc_player:
            self.vlc_player.audio_set_volume(volume)
    
    def slider_pressed(self):
        """进度条被按下"""
        self.timer.stop()
    
    def slider_released(self):
        """进度条被释放"""
        if self.vlc_player:
            position = self.position_slider.value()
            self.vlc_player.set_time(position)
        self.timer.start()
    
    def update_position(self):
        """更新播放位置"""
        if not self.vlc_player:
            return
            
        # 获取当前播放时间
        current_time = self.vlc_player.get_time()
        if current_time >= 0:
            # **关键逻辑：检查是否超过当前片段的结束时间**
            if (self.current_segment_end_time > 0 and 
                current_time >= self.current_segment_end_time):
                # 立即停止播放并定位到片段结束位置
                self.vlc_player.pause()
                self.vlc_player.set_time(self.current_segment_end_time)
                self.play_pause_btn.setText('播放')
                self.is_playing = False
                print(f"片段播放结束，停止在: {self.current_segment_end_time}ms")
                return
            
            # 更新进度条
            self.position_slider.setValue(current_time)
            
            # 更新时间显示
            duration = self.vlc_player.get_length()
            if duration > 0:
                current_time_str = self.format_time(current_time / 1000)
                total_time_str = self.format_time(duration / 1000)
                self.time_label.setText(f"{current_time_str} / {total_time_str}")
        
        # 检查播放状态
        state = self.vlc_player.get_state()
        if state == vlc.State.Ended:
            self.play_pause_btn.setText('播放')
            self.is_playing = False
        elif state == vlc.State.Playing:
            if not self.is_playing:
                self.play_pause_btn.setText('暂停')
                self.is_playing = True
        elif state == vlc.State.Paused:
            if self.is_playing:
                self.play_pause_btn.setText('播放')
                self.is_playing = False
    
    def on_segment_clicked(self, segment_index):
        """时间轴片段点击事件"""
        self.play_segment(segment_index)
    
    def on_segment_list_clicked(self, item):
        """片段列表点击事件"""
        segment_index = self.segments_list.row(item)
        self.play_segment(segment_index)
    
    def play_segment(self, segment_index):
        """播放指定片段"""
        if 0 <= segment_index < len(self.segments) and self.vlc_player:
            self.current_segment_index = segment_index
            segment = self.segments[segment_index]
            
            # 记录当前片段的结束时间（转换为毫秒）
            self.current_segment_end_time = int(segment['end'] * 1000)
            
            # 设置播放位置到片段开始
            start_ms = int(segment['start'] * 1000)
            self.vlc_player.set_time(start_ms)
            
            # 更新当前片段显示
            start_time = self.format_time(segment['start'])
            end_time = self.format_time(segment['end'])
            self.current_segment_label.setText(
                f"当前片段: 片段{segment_index + 1} [{start_time}-{end_time}]"
            )
            
            # 启用导出按钮
            self.export_segment_btn.setEnabled(True)
            
            # 开始播放
            self.vlc_player.play()
            self.play_pause_btn.setText('暂停')
            self.is_playing = True
            
            # 高亮选中的片段
            self.segments_list.setCurrentRow(segment_index)

    def start_transcription(self):
        if not self.video_path:
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.transcribe_btn.setEnabled(False)
        self.statusBar().showMessage('正在转录...')
        
        self.transcription_worker = TranscriptionWorker(self.video_path)
        self.transcription_worker.progress.connect(self.progress_bar.setValue)
        self.transcription_worker.finished.connect(self.on_transcription_finished)
        self.transcription_worker.error.connect(self.on_transcription_error)
        self.transcription_worker.start()
    
    def on_transcription_finished(self, segments):
        self.segments = segments
        self.progress_bar.setVisible(False)
        self.transcribe_btn.setEnabled(True)
        self.split_video_btn.setEnabled(True)
        
        # 更新UI
        self.update_transcript_display()
        self.update_timeline_display()
        self.update_segments_list()
        
        self.statusBar().showMessage(f'转录完成，共{len(segments)}个片段')
    
    def on_transcription_error(self, error_msg):
        self.progress_bar.setVisible(False)
        self.transcribe_btn.setEnabled(True)
        QMessageBox.critical(self, '转录错误', f'转录失败: {error_msg}')
        self.statusBar().showMessage('转录失败')
    
    def update_transcript_display(self):
        transcript_text = ""
        for segment in self.segments:
            start_time = self.format_time(segment['start'])
            end_time = self.format_time(segment['end'])
            transcript_text += f"[{start_time} - {end_time}] {segment['text']}\n\n"
        
        self.transcript_text.setPlainText(transcript_text)
    
    def update_timeline_display(self):
        self.video_timeline.set_segments(self.segments)
    
    def update_segments_list(self):
        self.segments_list.clear()
        for i, segment in enumerate(self.segments):
            start_time = self.format_time(segment['start'])
            end_time = self.format_time(segment['end'])
            text_preview = segment['text'][:50] + '...' if len(segment['text']) > 50 else segment['text']
            item_text = f"片段 {i+1}: [{start_time}-{end_time}] {text_preview}"
            self.segments_list.addItem(item_text)
    
    def split_video(self):
        if not self.segments:
            return
        
        output_dir = QFileDialog.getExistingDirectory(self, '选择输出目录')
        if not output_dir:
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.split_video_btn.setEnabled(False)
        self.statusBar().showMessage('正在分割视频...')
        
        self.video_worker = VideoProcessingWorker(self.video_path, self.segments, output_dir)
        self.video_worker.progress.connect(self.progress_bar.setValue)
        self.video_worker.finished.connect(self.on_video_processing_finished)
        self.video_worker.error.connect(self.on_video_processing_error)
        self.video_worker.start()
    
    def on_video_processing_finished(self):
        self.progress_bar.setVisible(False)
        self.split_video_btn.setEnabled(True)
        QMessageBox.information(self, '完成', '视频分割完成！')
        self.statusBar().showMessage('视频分割完成')
    
    def on_video_processing_error(self, error_msg):
        self.progress_bar.setVisible(False)
        self.split_video_btn.setEnabled(True)
        QMessageBox.critical(self, '分割错误', f'视频分割失败: {error_msg}')
        self.statusBar().showMessage('视频分割失败')
    
    def format_time(self, seconds):
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def export_current_segment(self):
        """导出当前选中的视频片段"""
        if self.current_segment_index < 0 or not self.segments or not self.video_path:
            QMessageBox.warning(self, '警告', '请先选择一个视频片段')
            return
        
        segment = self.segments[self.current_segment_index]
        
        # 生成默认文件名
        video_name = os.path.splitext(os.path.basename(self.video_path))[0]
        start_time = self.format_time(segment['start']).replace(':', '-')
        end_time = self.format_time(segment['end']).replace(':', '-')
        default_filename = f"{video_name}_片段{self.current_segment_index + 1}_{start_time}到{end_time}.mp4"
        
        # 设置默认保存目录
        default_dir = QStandardPaths.writableLocation(QStandardPaths.MoviesLocation)
        if not default_dir or not os.path.exists(default_dir):
            default_dir = os.path.expanduser('~')
        
        default_path = os.path.join(default_dir, default_filename)
        
        # 弹出文件保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            '保存视频片段', 
            default_path,
            'MP4 Files (*.mp4);;AVI Files (*.avi);;MOV Files (*.mov);;All Files (*)'
        )
        
        if file_path:
            # 确保文件扩展名
            if not os.path.splitext(file_path)[1]:
                file_path += '.mp4'
            
            # 开始导出
            self.export_single_segment(segment, file_path)

    def export_single_segment(self, segment, output_path):
        """导出单个视频片段"""
        try:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.export_segment_btn.setEnabled(False)
            self.statusBar().showMessage('正在导出视频片段...')
            
            # 创建视频处理器
            processor = VideoProcessor()
            
            # 创建单片段列表
            single_segment = [segment]
            
            # 获取输出目录和文件名
            output_dir = os.path.dirname(output_path)
            filename = os.path.basename(output_path)
            
            # 创建工作线程
            self.export_worker = VideoExportWorker(
                self.video_path, 
                single_segment, 
                output_dir,
                filename
            )
            self.export_worker.progress.connect(self.progress_bar.setValue)
            self.export_worker.finished.connect(self.on_export_finished)
            self.export_worker.error.connect(self.on_export_error)
            self.export_worker.start()
            
        except Exception as e:
            self.progress_bar.setVisible(False)
            self.export_segment_btn.setEnabled(True)
            QMessageBox.critical(self, '导出错误', f'导出失败: {str(e)}')
            self.statusBar().showMessage('导出失败')

    def on_export_finished(self):
        """导出完成回调"""
        self.progress_bar.setVisible(False)
        self.export_segment_btn.setEnabled(True)
        QMessageBox.information(self, '完成', '视频片段导出完成！')
        self.statusBar().showMessage('视频片段导出完成')

    def on_export_error(self, error_msg):
        """导出错误回调"""
        self.progress_bar.setVisible(False)
        self.export_segment_btn.setEnabled(True)
        QMessageBox.critical(self, '导出错误', f'视频片段导出失败: {error_msg}')
        self.statusBar().showMessage('视频片段导出失败')