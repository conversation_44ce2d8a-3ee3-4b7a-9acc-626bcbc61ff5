import os
import mimetypes

def is_video_file(file_path):
    """检查文件是否为视频文件"""
    video_extensions = {
        '.mp4', '.avi', '.mov', '.mkv', '.wmv', 
        '.flv', '.webm', '.m4v', '.3gp', '.ogv'
    }
    
    _, ext = os.path.splitext(file_path.lower())
    return ext in video_extensions

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    try:
        size_bytes = os.path.getsize(file_path)
        return size_bytes / (1024 * 1024)
    except:
        return 0

def ensure_dir_exists(dir_path):
    """确保目录存在"""
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)