import gradio as gr
import os
import shutil
import traceback
import random
import string
from core.whisper_transcriber import WhisperTranscriber
from core.video_processor import VideoProcessor

TEMP_OUTPUT_DIR = "temp_gradio_outputs"

def cleanup_temp_files(directory_path):
    if os.path.exists(directory_path):
        for item in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item)
            if os.path.isfile(item_path) or os.path.islink(item_path):
                os.unlink(item_path)
            elif os.path.isdir(item_path):
                shutil.rmtree(item_path)
    if directory_path == TEMP_OUTPUT_DIR and not os.path.exists(TEMP_OUTPUT_DIR):
        os.makedirs(TEMP_OUTPUT_DIR, exist_ok=True)

if os.path.exists(TEMP_OUTPUT_DIR):
    cleanup_temp_files(TEMP_OUTPUT_DIR)
    os.makedirs(TEMP_OUTPUT_DIR, exist_ok=True)
else:
    os.makedirs(TEMP_OUTPUT_DIR, exist_ok=True)

transcriber = WhisperTranscriber(model_size="base")
video_processor = VideoProcessor()

# Define process_video and update_preview_video functions BEFORE the gr.Blocks context
def process_video(video_path, progress=gr.Progress(track_tqdm=True)):
    if not video_path:
        return gr.update(interactive=True), "请先上传视频文件", [], None, "", ""

    unique_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    current_temp_dir = os.path.join(TEMP_OUTPUT_DIR, unique_id)
    os.makedirs(current_temp_dir, exist_ok=True)
    cleanup_temp_files(current_temp_dir)

    yield gr.update(value="处理中...", interactive=False), "", [], None, "", ""

    try:
        segments_data = transcriber.transcribe(video_path)
        full_transcript = " ".join([segment['text'] for segment in segments_data])
        yield gr.update(value="转录完成，正在分割视频..."), full_transcript, [], None, "", ""

        # Corrected method call and handling of segment_files_info
        video_processor.split_video(
            video_path, 
            segments_data, 
            output_dir=current_temp_dir
        )

        # Manually construct segment_files_info and segment_options
        segment_options = []
        base_name = os.path.splitext(os.path.basename(video_path))[0]
        for i, seg_data in enumerate(segments_data):
            start_time_str = video_processor.format_time_for_filename(seg_data['start']) # Use helper from VideoProcessor
            # Ensure the filename format matches exactly what VideoProcessor.split_video creates
            output_filename = f"{base_name}_片段{i+1:03d}_{start_time_str}.mp4"
            file_path = os.path.join(current_temp_dir, output_filename)
            
            if os.path.exists(file_path): # Check if file was actually created
                display_text = f"{seg_data['text'][:50]}..."
                segment_options.append((display_text, file_path))
            else:
                print(f"警告: 未找到预期的片段文件: {file_path}")

        if not segment_options:
            print("警告: segment_options 为空。没有片段被成功处理或找到。")

        initial_preview_path = segment_options[0][1] if segment_options else None

        yield (
            gr.update(value="处理完成", interactive=True),
            full_transcript,
            gr.update(choices=segment_options, value=initial_preview_path if segment_options else None, interactive=True),
            gr.update(value=initial_preview_path, interactive=True),
            "处理成功完成！",
            ""
        )

    except Exception as e:
        error_message = f"处理视频时发生错误: {str(e)}"
        print(f"{error_message}\n{traceback.format_exc()}")
        yield (
            gr.update(value="处理失败", interactive=True),
            "",
            gr.update(choices=[], value=None, interactive=False),
            gr.update(value=None, interactive=False),
            error_message,
            traceback.format_exc()
        )

def update_preview_video(selected_segment_path):
    if selected_segment_path and os.path.exists(selected_segment_path):
        return gr.update(value=selected_segment_path, interactive=True)
    return gr.update(value=None, interactive=True) # Or some placeholder/error message

# Gradio Interface
with gr.Blocks(theme=gr.themes.Soft()) as demo:
    gr.Markdown("## 视频片段智能剪辑工具 🎬")
    gr.Markdown("上传视频，自动生成字幕并按字幕分割成片段，可预览和下载片段。")
    
    with gr.Row():
        with gr.Column(scale=1):
            video_input = gr.Video(label="上传视频", sources=["upload"])
            process_button = gr.Button("开始处理", interactive=False)
            
            full_transcript_output = gr.Textbox(
                label="完整转录内容", 
                lines=10, 
                interactive=False
            )
            status_output = gr.Textbox(label="处理状态", interactive=False)
            error_output = gr.Textbox(label="错误信息", interactive=False, visible=False)

        with gr.Column(scale=1):
            segment_selector = gr.Dropdown(
                label="选择预览片段", 
                choices=[], 
                interactive=False
            )
            preview_video_player = gr.Video(label="片段预览", interactive=False)

    def on_video_upload(video_path):
        if video_path:
            return gr.update(interactive=True)
        return gr.update(interactive=False)

    video_input.upload(
        on_video_upload,
        inputs=[video_input],
        outputs=[process_button]
    )
    
    process_button.click(
        process_video, # Now defined globally
        inputs=[video_input],
        outputs=[
            process_button, 
            full_transcript_output, 
            segment_selector, 
            preview_video_player,
            status_output,
            error_output
        ]
    )

    segment_selector.change(
        update_preview_video, # Now defined globally
        inputs=[segment_selector],
        outputs=[preview_video_player]
    )

demo.queue().launch()